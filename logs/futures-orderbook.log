2025-08-20 16:57:51.768 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:57:51.769 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 16:57:51.769 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 16:57:51.815 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 16:57:51.910 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 16:57:51.911 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 16:57:52.248 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:57:52.284 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:57:52.299 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:57:52.449 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:57:52.450 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:57:52.510 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:57:52.510 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:57:52.606 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:57:52.606 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:57:52.730 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 16:57:52.749 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:57:52.749 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:57:57.750 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                               ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:07.865 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.914 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:12.914 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.970 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:17.970 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:17.970 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:23.015 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:23.015 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:28.067 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:28.068 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:28.068 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:28.068 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:33.114 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:33.115 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:33.115 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:33.115 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:38.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:38.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:38.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:48.252 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:53.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:53.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:53.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:58.370 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:23.602 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:23.602 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:59.534 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:59:59.536 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 16:59:59.537 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:59:59.540 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:59:59.540 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 16:59:59.540 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 16:59:59.585 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 16:59:59.686 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 16:59:59.686 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 17:00:00.066 [flink-pekko.actor.default-dispatcher-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:00:00.108 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:00:00.127 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:00:00.295 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 17:00:00.295 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 17:00:00.354 [flink-pekko.actor.default-dispatcher-5] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:00:00.354 [flink-pekko.actor.default-dispatcher-5] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 17:00:00.456 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:00:00.456 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:00:00.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 17:00:00.605 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:00:00.605 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:00:00.760 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #1                  ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: JD2504-C-3200                ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:00.761                 ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:     4.50  数量:        5     ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                               ║
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                        1 ║
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:05.817 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:05.817 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #47                 ║
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:05.816                 ║
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                       47 ║
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:10.879 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:10.881 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #97                 ║
2025-08-20 17:00:10.881 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:10.881 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:10.879                 ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                       97 ║
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:15.932 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:15.932 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #147                ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:15.931                 ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      147 ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:20.978 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:20.978 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #197                ║
2025-08-20 17:00:20.978 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:20.977                 ║
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      197 ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:26.026 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #247                ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:26.026                 ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      247 ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:31.076 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:31.078 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #297                ║
2025-08-20 17:00:31.079 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:31.079 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:31.076                 ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      297 ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:36.131 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #347                ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:36.131                 ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      347 ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:41.180 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #397                ║
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:41.180                 ║
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      397 ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:46.230 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:46.231 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #447                ║
2025-08-20 17:00:46.231 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:46.230                 ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      447 ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:51.271 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #497                ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:51.271                 ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      497 ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:56.323 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:56.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #547                ║
2025-08-20 17:00:56.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:56.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:56.323                 ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      547 ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:01:01.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:01:01.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #597                ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:01:01.371                 ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      597 ║
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:01:06.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:01:06.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #647                ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:01:06.429                 ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:06.433 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      647 ║
2025-08-20 17:01:06.433 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #697                ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:01:11.471                 ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      697 ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:31:21.050 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:31:21.053 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 17:31:21.054 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:31:21.057 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 17:31:21.057 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 17:31:21.057 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 17:31:21.155 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 17:31:21.341 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 17:31:21.341 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 17:31:21.875 [flink-pekko.actor.default-dispatcher-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:31:21.940 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:31:21.960 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:31:22.203 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 17:31:22.203 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 17:31:22.286 [flink-pekko.actor.default-dispatcher-6] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:31:22.287 [flink-pekko.actor.default-dispatcher-6] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 17:31:22.411 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:31:22.411 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:31:22.536 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 17:31:22.565 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:31:22.565 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:31:40.005 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:31:40.006 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 17:31:40.006 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:31:40.007 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 17:31:40.008 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 17:31:40.008 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 17:31:40.053 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 17:31:40.177 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 17:31:40.178 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 17:31:40.533 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:31:40.569 [flink-metrics-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:31:40.584 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:31:40.724 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 17:31:40.724 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 17:31:40.781 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:31:40.781 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 17:31:40.872 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:31:40.872 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:31:40.929 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 17:31:40.992 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:31:40.992 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:31:41.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 17:31:41.099 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #1                             2025-08-20 17:31:41.099 ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: JD2504-C-3200                                               ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:31:41.099                                     ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:31:41.100                                     ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共1档):                                                      ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:       4.50  |  数量:        5        (1笔)         ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                                                           ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 1           |  快照间隔: 500ms                     ║
2025-08-20 17:31:41.100 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:31:46.154 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:31:46.155 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #47                            2025-08-20 17:31:46.153 ║
2025-08-20 17:31:46.155 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:46.155 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:31:46.155 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:31:46.153                                     ║
2025-08-20 17:31:46.156 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:31:46.156                                     ║
2025-08-20 17:31:46.156 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:46.156 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:31:46.156 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:31:46.156 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:46.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:46.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:46.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:46.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共4档):                                                      ║
2025-08-20 17:31:46.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:31:46.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:46.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:46.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:31:46.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:46.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:46.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:31:46.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:46.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 47          |  快照间隔: 500ms                     ║
2025-08-20 17:31:46.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:31:51.209 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:31:51.211 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #97                            2025-08-20 17:31:51.209 ║
2025-08-20 17:31:51.211 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:51.211 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:31:51.212 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:31:51.209                                     ║
2025-08-20 17:31:51.212 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:31:51.212                                     ║
2025-08-20 17:31:51.212 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:51.212 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:31:51.212 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:31:51.212 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:51.213 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:51.213 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:51.213 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:51.213 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:31:51.213 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:31:51.213 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:51.213 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 97          |  快照间隔: 500ms                     ║
2025-08-20 17:31:51.214 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:31:56.261 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #147                           2025-08-20 17:31:56.260 ║
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:31:56.260                                     ║
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:31:56.262                                     ║
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:31:56.262 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:31:56.263 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:56.263 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:56.263 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:56.263 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:56.263 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:31:56.263 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:31:56.264 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:56.264 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:31:56.264 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:31:56.264 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:31:56.264 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:31:56.264 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:56.265 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:31:56.265 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:31:56.265 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 147         |  快照间隔: 500ms                     ║
2025-08-20 17:31:56.265 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:01.322 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:01.323 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #197                           2025-08-20 17:32:01.321 ║
2025-08-20 17:32:01.323 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:01.323 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:01.323 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:01.321                                     ║
2025-08-20 17:32:01.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:01.323                                     ║
2025-08-20 17:32:01.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:01.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:01.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:01.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:01.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:01.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:01.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:01.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:01.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:01.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:01.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:01.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:01.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:01.327 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:01.327 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:01.327 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:01.327 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:01.327 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 197         |  快照间隔: 500ms                     ║
2025-08-20 17:32:01.327 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:06.384 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #247                           2025-08-20 17:32:06.384 ║
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:06.384                                     ║
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:06.385                                     ║
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:06.385 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:06.386 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:06.387 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:06.387 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:06.387 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:06.387 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:06.387 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:06.387 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 247         |  快照间隔: 500ms                     ║
2025-08-20 17:32:06.387 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:11.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #297                           2025-08-20 17:32:11.426 ║
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:11.426                                     ║
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:11.428                                     ║
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:11.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:11.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:11.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:11.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:11.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:11.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:11.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:11.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 297         |  快照间隔: 500ms                     ║
2025-08-20 17:32:11.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:16.481 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #347                           2025-08-20 17:32:16.481 ║
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:16.481                                     ║
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:16.482                                     ║
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:16.482 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 347         |  快照间隔: 500ms                     ║
2025-08-20 17:32:16.483 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:21.540 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:21.541 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #397                           2025-08-20 17:32:21.539 ║
2025-08-20 17:32:21.541 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:21.542 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:21.542 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:21.539                                     ║
2025-08-20 17:32:21.542 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:21.542                                     ║
2025-08-20 17:32:21.542 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:21.543 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:21.543 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:21.543 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:21.543 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:21.543 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:21.543 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:21.544 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:21.544 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:21.544 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:21.544 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:21.544 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:21.544 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:21.544 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:21.545 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:21.545 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:21.545 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:21.545 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 397         |  快照间隔: 500ms                     ║
2025-08-20 17:32:21.545 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:26.588 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #447                           2025-08-20 17:32:26.588 ║
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:26.588                                     ║
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:26.589                                     ║
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:26.589 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:26.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:26.591 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:26.591 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:26.594 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:26.594 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:26.594 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 447         |  快照间隔: 500ms                     ║
2025-08-20 17:32:26.594 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:31.638 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:31.640 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #497                           2025-08-20 17:32:31.638 ║
2025-08-20 17:32:31.640 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:31.640 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:31.640 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:31.638                                     ║
2025-08-20 17:32:31.651 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:31.640                                     ║
2025-08-20 17:32:31.651 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:31.652 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:31.653 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:31.653 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:31.653 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 497         |  快照间隔: 500ms                     ║
2025-08-20 17:32:31.653 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:36.707 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #547                           2025-08-20 17:32:36.707 ║
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:36.707                                     ║
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:36.708                                     ║
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:36.708 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 547         |  快照间隔: 500ms                     ║
2025-08-20 17:32:36.709 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:41.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #597                           2025-08-20 17:32:41.743 ║
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:41.743                                     ║
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:41.745                                     ║
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:41.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:41.746 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:41.747 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:41.747 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:41.747 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 597         |  快照间隔: 500ms                     ║
2025-08-20 17:32:41.747 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:46.790 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:46.790 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #647                           2025-08-20 17:32:46.790 ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:46.790                                     ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:46.791                                     ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:46.791 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 647         |  快照间隔: 500ms                     ║
2025-08-20 17:32:46.792 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:51.838 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:51.839 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #697                           2025-08-20 17:32:51.838 ║
2025-08-20 17:32:51.839 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:51.839 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:51.839 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:51.838                                     ║
2025-08-20 17:32:51.839 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:51.839                                     ║
2025-08-20 17:32:51.839 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:51.839 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:51.840 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:51.841 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:51.841 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:51.841 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:51.841 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:51.841 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:51.841 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 697         |  快照间隔: 500ms                     ║
2025-08-20 17:32:51.841 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:32:56.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:32:56.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #747                           2025-08-20 17:32:56.881 ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:32:56.881                                     ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:32:56.883                                     ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:56.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 747         |  快照间隔: 500ms                     ║
2025-08-20 17:32:56.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:01.932 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:01.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #797                           2025-08-20 17:33:01.932 ║
2025-08-20 17:33:01.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:01.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:01.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:01.932                                     ║
2025-08-20 17:33:01.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:01.934                                     ║
2025-08-20 17:33:01.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:01.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:01.936 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:33:01.936 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:01.936 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:01.936 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:01.936 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:01.936 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 797         |  快照间隔: 500ms                     ║
2025-08-20 17:33:01.936 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:06.984 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #847                           2025-08-20 17:33:06.984 ║
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:06.984                                     ║
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:06.985                                     ║
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:06.985 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:06.986 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:06.987 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 847         |  快照间隔: 500ms                     ║
2025-08-20 17:33:06.987 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:12.033 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #897                           2025-08-20 17:33:12.033 ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:12.033                                     ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:12.035                                     ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:12.035 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:12.036 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:33:12.036 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:12.036 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:12.036 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:12.036 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:12.036 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 897         |  快照间隔: 500ms                     ║
2025-08-20 17:33:12.036 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:16.960 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:33:16.962 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 17:33:16.962 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:33:16.964 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 17:33:16.965 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 17:33:16.965 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 17:33:17.024 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 17:33:17.114 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 17:33:17.114 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 17:33:17.466 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:33:17.504 [flink-metrics-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:33:17.521 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:33:17.664 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 17:33:17.664 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 17:33:17.719 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:33:17.719 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 17:33:17.815 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:33:17.815 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:33:17.937 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 17:33:17.951 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:33:17.951 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:33:25.238 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 17:33:25.241 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #1                             2025-08-20 17:33:25.241 ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: JD2504-C-3200                                               ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:25.241                                     ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:25.242                                     ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共1档):                                                      ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:       4.50  |  数量:        5        (1笔)         ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                                                           ║
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:25.242 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 1           |  快照间隔: 500ms                     ║
2025-08-20 17:33:25.243 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:30.307 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:30.308 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #35                            2025-08-20 17:33:30.307 ║
2025-08-20 17:33:30.308 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:30.308 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:30.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:30.307                                     ║
2025-08-20 17:33:30.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:30.309                                     ║
2025-08-20 17:33:30.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:30.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共1档):                                                      ║
2025-08-20 17:33:30.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:30.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:30.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:30.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共3档):                                                      ║
2025-08-20 17:33:30.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:30.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:30.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:30.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:30.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:30.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:30.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:30.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 35          |  快照间隔: 500ms                     ║
2025-08-20 17:33:30.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:35.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:35.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #85                            2025-08-20 17:33:35.371 ║
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:35.371                                     ║
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:35.373                                     ║
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:35.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:35.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:35.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:35.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:35.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共4档):                                                      ║
2025-08-20 17:33:35.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:35.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:35.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:35.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:35.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        7        (1笔)         ║
2025-08-20 17:33:35.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:35.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:35.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:35.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 85          |  快照间隔: 500ms                     ║
2025-08-20 17:33:35.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:40.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:40.427 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #135                           2025-08-20 17:33:40.426 ║
2025-08-20 17:33:40.427 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:40.426                                     ║
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:40.428                                     ║
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:40.428 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共6档):                                                      ║
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:40.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:40.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:33:40.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  6] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:40.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:40.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:40.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:40.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 135         |  快照间隔: 500ms                     ║
2025-08-20 17:33:40.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:45.488 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:45.490 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #185                           2025-08-20 17:33:45.488 ║
2025-08-20 17:33:45.490 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:45.490 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:45.490 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:45.488                                     ║
2025-08-20 17:33:45.490 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:45.490                                     ║
2025-08-20 17:33:45.490 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:45.491 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:45.491 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:45.491 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:45.491 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:45.491 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:45.491 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:45.491 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共6档):                                                      ║
2025-08-20 17:33:45.492 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:45.492 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:45.492 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:45.492 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:45.492 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:45.492 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:33:45.493 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  6] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:45.493 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:45.493 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:45.493 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:45.493 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 185         |  快照间隔: 500ms                     ║
2025-08-20 17:33:45.493 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:50.529 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:50.530 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #235                           2025-08-20 17:33:50.528 ║
2025-08-20 17:33:50.530 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:50.530 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:50.530 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:50.528                                     ║
2025-08-20 17:33:50.531 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:50.530                                     ║
2025-08-20 17:33:50.531 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:50.531 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:50.531 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:50.531 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共6档):                                                      ║
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:50.532 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  6] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 235         |  快照间隔: 500ms                     ║
2025-08-20 17:33:50.533 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:33:55.595 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:33:55.596 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #285                           2025-08-20 17:33:55.594 ║
2025-08-20 17:33:55.596 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:33:55.594                                     ║
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:33:55.597                                     ║
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:55.597 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共6档):                                                      ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        1        (1笔)         ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  6] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:33:55.598 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:55.599 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:33:55.599 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:33:55.599 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 285         |  快照间隔: 500ms                     ║
2025-08-20 17:33:55.599 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:00.655 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:00.656 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #335                           2025-08-20 17:34:00.655 ║
2025-08-20 17:34:00.656 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:00.656 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:00.656 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:00.655                                     ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:00.657                                     ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共6档):                                                      ║
2025-08-20 17:34:00.657 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  6] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 335         |  快照间隔: 500ms                     ║
2025-08-20 17:34:00.658 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:07.113 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:34:07.114 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 17:34:07.114 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:34:07.115 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 17:34:07.116 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 17:34:07.116 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 17:34:07.154 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 17:34:07.243 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 17:34:07.243 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 17:34:07.605 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:34:07.642 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:34:07.660 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:34:07.792 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 17:34:07.792 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 17:34:07.848 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:34:07.849 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 17:34:07.943 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:34:07.943 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:34:07.999 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 17:34:08.062 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:34:08.062 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:34:14.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #1                             2025-08-20 17:34:14.469 ║
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: JD2504-C-3200                                               ║
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:14.469                                     ║
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:14.470                                     ║
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共1档):                                                      ║
2025-08-20 17:34:14.470 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:       4.50  |  数量:        5        (1笔)         ║
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                                                           ║
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 1           |  快照间隔: 500ms                     ║
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 1 笔订单, 5 手  |  虚拟层: 0 笔订单, 0 手       ║
2025-08-20 17:34:14.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:19.523 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:19.524 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #34                            2025-08-20 17:34:19.523 ║
2025-08-20 17:34:19.524 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:19.525 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:19.525 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:19.523                                     ║
2025-08-20 17:34:19.525 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:19.525                                     ║
2025-08-20 17:34:19.526 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:19.526 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共1档):                                                      ║
2025-08-20 17:34:19.526 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:19.527 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:19.527 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:19.527 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共3档):                                                      ║
2025-08-20 17:34:19.527 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:19.527 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:19.527 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:19.528 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:19.528 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:19.528 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:19.528 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:19.528 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 34          |  快照间隔: 500ms                     ║
2025-08-20 17:34:19.528 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:19.529 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 4 笔订单, 8 手  |  虚拟层: 0 笔订单, 0 手       ║
2025-08-20 17:34:19.529 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:24.576 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:24.577 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #89                            2025-08-20 17:34:24.576 ║
2025-08-20 17:34:24.578 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:24.578 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:24.578 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:24.576                                     ║
2025-08-20 17:34:24.578 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:24.578                                     ║
2025-08-20 17:34:24.579 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:24.579 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:24.579 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:24.579 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:24.579 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共4档):                                                      ║
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:24.580 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:        6        (1笔)         ║
2025-08-20 17:34:24.581 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:24.581 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:24.581 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:24.581 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 89          |  快照间隔: 500ms                     ║
2025-08-20 17:34:24.581 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:24.581 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 1 笔订单, 6 手       ║
2025-08-20 17:34:24.581 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:29.633 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #139                           2025-08-20 17:34:29.632 ║
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:29.632                                     ║
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:29.634                                     ║
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:29.634 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:29.635 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:29.636 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:29.636 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:29.636 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:29.636 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:29.636 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:29.637 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:29.637 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 139         |  快照间隔: 500ms                     ║
2025-08-20 17:34:29.637 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:29.637 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:34:29.637 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:34.690 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:34.692 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #189                           2025-08-20 17:34:34.690 ║
2025-08-20 17:34:34.692 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:34.692 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:34.692 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:34.690                                     ║
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:34.693                                     ║
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:34.693 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:34.694 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:34:34.694 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:34.695 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:34.695 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:34.695 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:34.695 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 189         |  快照间隔: 500ms                     ║
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:34:34.696 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:39.741 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:39.742 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #239                           2025-08-20 17:34:39.740 ║
2025-08-20 17:34:39.742 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:39.740                                     ║
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:39.743                                     ║
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:39.743 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:39.744 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 239         |  快照间隔: 500ms                     ║
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:34:39.745 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:44.796 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:44.798 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #289                           2025-08-20 17:34:44.796 ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:44.796                                     ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:44.799                                     ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:44.799 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 289         |  快照间隔: 500ms                     ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:34:44.800 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:49.844 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #339                           2025-08-20 17:34:49.844 ║
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:49.844                                     ║
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:49.845                                     ║
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:49.845 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:49.846 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 339         |  快照间隔: 500ms                     ║
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:34:49.847 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #389                           2025-08-20 17:34:54.898 ║
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:54.898                                     ║
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:54.899                                     ║
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:54.899 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:54.900 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:54.901 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:54.901 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 389         |  快照间隔: 500ms                     ║
2025-08-20 17:34:54.901 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:54.901 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:34:54.901 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:34:59.943 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:34:59.944 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #439                           2025-08-20 17:34:59.943 ║
2025-08-20 17:34:59.944 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:59.944 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:34:59.944 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:34:59.943                                     ║
2025-08-20 17:34:59.944 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:34:59.944                                     ║
2025-08-20 17:34:59.945 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:59.945 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:34:59.945 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:59.945 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:59.945 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:59.945 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:34:59.946 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:59.947 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 439         |  快照间隔: 500ms                     ║
2025-08-20 17:34:59.947 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:34:59.947 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:34:59.947 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:04.992 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #489                           2025-08-20 17:35:04.992 ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:04.992                                     ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:04.993                                     ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:04.993 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 489         |  快照间隔: 500ms                     ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:04.994 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:10.041 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:10.042 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #539                           2025-08-20 17:35:10.041 ║
2025-08-20 17:35:10.042 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:10.042 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:10.042 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:10.041                                     ║
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:10.043                                     ║
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:10.043 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 539         |  快照间隔: 500ms                     ║
2025-08-20 17:35:10.044 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:10.045 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:10.045 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #589                           2025-08-20 17:35:15.096 ║
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:15.096                                     ║
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:15.096                                     ║
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:15.096 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 589         |  快照间隔: 500ms                     ║
2025-08-20 17:35:15.097 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:15.098 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:15.098 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:20.141 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:20.142 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #639                           2025-08-20 17:35:20.141 ║
2025-08-20 17:35:20.142 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:20.143 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:20.143 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:20.141                                     ║
2025-08-20 17:35:20.143 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:20.143                                     ║
2025-08-20 17:35:20.143 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:20.143 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:20.143 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:20.144 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 639         |  快照间隔: 500ms                     ║
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:20.145 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #689                           2025-08-20 17:35:25.193 ║
2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:25.193                                     ║
2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:25.194                                     ║
2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:25.194 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 689         |  快照间隔: 500ms                     ║
2025-08-20 17:35:25.195 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:25.196 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:25.196 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:30.246 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #739                           2025-08-20 17:35:30.246 ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:30.246                                     ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:30.247                                     ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:30.247 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 739         |  快照间隔: 500ms                     ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:30.248 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:35.290 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:35.291 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #789                           2025-08-20 17:35:35.290 ║
2025-08-20 17:35:35.291 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:35.291 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:35.291 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:35.290                                     ║
2025-08-20 17:35:35.291 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:35.291                                     ║
2025-08-20 17:35:35.291 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:35.291 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:35.292 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:35.292 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:35.292 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:35.292 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:35.292 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:35.292 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 789         |  快照间隔: 500ms                     ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:35.293 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:35:40.339 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #839                           2025-08-20 17:35:40.339 ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:35:40.339                                     ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:35:40.340                                     ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共5档):                                                      ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:35:40.340 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8072.50  |  数量:        2        (1笔)         ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  5] 价格:    8075.00  |  数量:        2        (1笔)         ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 839         |  快照间隔: 500ms                     ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 2 笔订单, 4 手       ║
2025-08-20 17:35:40.341 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:41:17.443 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:41:17.445 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 17:41:17.445 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 17:41:17.450 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 17:41:17.451 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 17:41:17.451 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 17:41:17.491 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 17:41:17.579 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 17:41:17.579 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 17:41:17.990 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:41:18.037 [flink-metrics-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:41:18.055 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:41:18.274 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 17:41:18.274 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 17:41:18.414 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:41:18.414 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 17:41:18.639 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:41:18.639 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:41:18.849 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 17:41:18.857 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:41:18.857 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:41:43.305 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 17:41:43.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:41:43.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #1                             2025-08-20 17:41:43.308 ║
2025-08-20 17:41:43.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:43.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: JD2504-C-3200                                               ║
2025-08-20 17:41:43.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:41:43.308                                     ║
2025-08-20 17:41:43.309 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:41:43.309                                     ║
2025-08-20 17:41:43.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:43.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共1档):                                                      ║
2025-08-20 17:41:43.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:41:43.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:       4.50  |  数量:        5        (1笔)         ║
2025-08-20 17:41:43.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:43.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                                                           ║
2025-08-20 17:41:43.310 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:43.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 1           |  快照间隔: 500ms                     ║
2025-08-20 17:41:43.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:43.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 1 笔订单, 5 手  |  虚拟层: 0 笔订单, 0 手       ║
2025-08-20 17:41:43.311 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:41:48.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:41:48.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #34                            2025-08-20 17:41:48.372 ║
2025-08-20 17:41:48.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:48.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:41:48.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:41:48.372                                     ║
2025-08-20 17:41:48.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:41:48.374                                     ║
2025-08-20 17:41:48.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:48.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共2档):                                                      ║
2025-08-20 17:41:48.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:41:48.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8037.00  |  数量:        1        (1笔)         ║
2025-08-20 17:41:48.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:41:48.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:48.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共3档):                                                      ║
2025-08-20 17:41:48.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:41:48.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:41:48.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:41:48.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:41:48.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:48.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8037.00  |  最优卖价:    8038.00  |  价差:     1.00   ║
2025-08-20 17:41:48.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:48.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 34          |  快照间隔: 500ms                     ║
2025-08-20 17:41:48.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:41:48.377 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 5 笔订单, 9 手  |  虚拟层: 0 笔订单, 0 手       ║
2025-08-20 17:41:48.377 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:42:42.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 13.0)
2025-08-20 17:42:42.474 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════════════════════════════════╗
2025-08-20 17:42:42.475 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║  订单簿快照 #104                           2025-08-20 17:42:42.473 ║
2025-08-20 17:42:42.475 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                                                      ║
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照生成时间: 17:42:42.473                                     ║
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 系统当前时间: 17:42:42.476                                     ║
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (共3档):                                                      ║
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8035.00  |  数量:        1        (1笔)         ║
2025-08-20 17:42:42.476 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8031.00  |  数量:        2        (1笔)         ║
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8030.00  |  数量:        2        (1笔)         ║
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (共4档):                                                      ║
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ ------------------------------------------------------------------- ║
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  1] 价格:    8038.00  |  数量:        1        (1笔)         ║
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  2] 价格:    8039.00  |  数量:        1        (1笔)         ║
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  3] 价格:    8040.00  |  数量:        5        (1笔)         ║
2025-08-20 17:42:42.477 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   [  4] 价格:    8059.00  |  数量:       13        (1笔)         ║
2025-08-20 17:42:42.478 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:42:42.478 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 最优买价:    8035.00  |  最优卖价:    8038.00  |  价差:     3.00   ║
2025-08-20 17:42:42.478 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:42:42.478 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: 104         |  快照间隔: 500ms                     ║
2025-08-20 17:42:42.478 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════════════════════════════════╣
2025-08-20 17:42:42.478 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 基础层: 6 笔订单, 12 手  |  虚拟层: 1 笔订单, 13 手       ║
2025-08-20 17:42:42.478 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════════════════════════════════╝

2025-08-20 17:42:42.480 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 12.0)
2025-08-20 17:42:42.481 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 11.0)
2025-08-20 17:42:42.481 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 10.0)
2025-08-20 17:42:42.481 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 9.0)
2025-08-20 17:42:42.481 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 8.0)
2025-08-20 17:42:42.495 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 7.0)
2025-08-20 17:42:42.495 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 6.0)
2025-08-20 17:42:42.495 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 4.0)
2025-08-20 17:42:42.495 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 2.0)
2025-08-20 17:42:42.495 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257414_L2_V @ 8059.0 (vol: 1.0)
2025-08-20 17:42:42.496 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257400_L2_V @ 8070.0 (vol: 4.0)
2025-08-20 17:42:42.496 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257401_L2_V @ 8075.0 (vol: 2.0)
2025-08-20 17:42:42.496 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.DualLayerOrderBookManager - Created virtual order for leg2: EB2505 S 100257400_L2_V @ 8072.5 (vol: 2.0)
