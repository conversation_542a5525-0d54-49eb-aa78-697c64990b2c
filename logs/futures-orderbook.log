2025-08-20 16:57:51.768 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:57:51.769 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 16:57:51.769 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 16:57:51.815 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 16:57:51.910 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 16:57:51.911 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 16:57:52.248 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:57:52.284 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:57:52.299 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:57:52.449 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:57:52.450 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:57:52.510 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:57:52.510 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:57:52.606 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:57:52.606 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:57:52.730 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 16:57:52.749 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:57:52.749 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:57:57.750 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                               ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:07.865 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.914 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:12.914 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.969 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:17.970 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:17.970 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:17.970 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:23.015 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:23.015 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.016 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:23.017 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:28.067 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:28.068 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:28.068 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:28.068 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:28.069 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:28.070 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:33.114 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:33.115 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:33.115 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:33.115 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.116 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:33.117 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:38.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:38.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:38.157 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:38.158 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:38.159 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.203 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:43.204 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:48.252 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:48.253 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:48.254 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:53.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:53.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:53.312 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.313 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:53.314 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:58.370 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:58.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:58.372 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:03.425 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:03.426 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.467 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:08.468 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:13.506 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:13.507 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:18.554 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:18.555 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:23.602 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:59:23.602 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:59:23.603 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:59:59.534 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:59:59.536 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 16:59:59.537 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:59:59.540 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:59:59.540 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 16:59:59.540 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 16:59:59.585 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 16:59:59.686 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 16:59:59.686 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 17:00:00.066 [flink-pekko.actor.default-dispatcher-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:00:00.108 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 17:00:00.127 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:00:00.295 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 17:00:00.295 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 17:00:00.354 [flink-pekko.actor.default-dispatcher-5] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 17:00:00.354 [flink-pekko.actor.default-dispatcher-5] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 17:00:00.456 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:00:00.456 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 17:00:00.590 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 17:00:00.605 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:00:00.605 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 17:00:00.760 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #1                  ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: JD2504-C-3200                ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:00.761                 ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:     4.50  数量:        5     ║
2025-08-20 17:00:00.762 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                               ║
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                        1 ║
2025-08-20 17:00:00.763 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:05.817 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:05.817 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #47                 ║
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:05.816                 ║
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:05.818 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:05.819 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:05.820 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                       47 ║
2025-08-20 17:00:05.821 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:10.879 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:10.881 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #97                 ║
2025-08-20 17:00:10.881 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:10.881 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:10.879                 ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:10.882 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:10.883 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                       97 ║
2025-08-20 17:00:10.884 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:15.932 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:15.932 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #147                ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:15.931                 ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:15.933 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:15.934 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      147 ║
2025-08-20 17:00:15.935 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:20.978 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:20.978 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #197                ║
2025-08-20 17:00:20.978 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:20.977                 ║
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:20.979 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:20.980 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      197 ║
2025-08-20 17:00:20.981 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:26.026 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #247                ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:26.026                 ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:26.027 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      247 ║
2025-08-20 17:00:26.028 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:31.076 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:31.078 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #297                ║
2025-08-20 17:00:31.079 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:31.079 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:31.076                 ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:31.080 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      297 ║
2025-08-20 17:00:31.081 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:36.131 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #347                ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:36.131                 ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:36.132 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      347 ║
2025-08-20 17:00:36.133 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:41.180 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #397                ║
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:41.180                 ║
2025-08-20 17:00:41.182 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:41.183 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      397 ║
2025-08-20 17:00:41.184 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:46.230 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:46.231 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #447                ║
2025-08-20 17:00:46.231 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:46.230                 ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:46.232 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      447 ║
2025-08-20 17:00:46.233 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:51.271 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #497                ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:51.271                 ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:51.273 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      497 ║
2025-08-20 17:00:51.274 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:00:56.323 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:00:56.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #547                ║
2025-08-20 17:00:56.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:56.324 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:00:56.323                 ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:00:56.325 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      547 ║
2025-08-20 17:00:56.326 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:01:01.371 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:01:01.373 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #597                ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:01:01.371                 ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:01:01.374 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:01:01.375 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      597 ║
2025-08-20 17:01:01.376 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:01:06.429 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:01:06.430 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #647                ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:01:06.429                 ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:01:06.431 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:01:06.432 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:06.433 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      647 ║
2025-08-20 17:01:06.433 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #697                ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: EB2505                       ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: 17:01:11.471                 ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8035.00  数量:        1     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8031.00  数量:        2     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8030.00  数量:        2     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8038.00  数量:        1     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8039.00  数量:        1     ║
2025-08-20 17:01:11.471 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8040.00  数量:        5     ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8072.50  数量:        2     ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格:  8075.00  数量:        2     ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照:                      697 ║
2025-08-20 17:01:11.472 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

