2025-08-20 16:33:01.753 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Starting Futures Order Book Kafka Job...
2025-08-20 16:33:01.756 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:33:02.303 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:33:02.342 [flink-metrics-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4j<PERSON>ogger started
2025-08-20 16:33:02.361 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:33:02.532 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:33:02.532 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:33:02.602 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:33:02.603 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:33:02.752 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:33:02.752 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:33:02.752 [SourceCoordinator-Source: Trades] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:33:02.930 [Source: Combination Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:33:02.930 [Source: Single Leg Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:33:02.930 [Source: Trades -> Filter (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:34:51.353 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Starting Futures Order Book Kafka Job...
2025-08-20 16:34:51.354 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:34:51.835 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:34:51.869 [flink-metrics-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:34:51.887 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:34:52.041 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:34:52.041 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:34:52.097 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:34:52.097 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:34:52.200 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:34:52.200 [SourceCoordinator-Source: Trades] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:34:52.200 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:34:52.348 [Source: Trades -> Filter (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:34:52.348 [Source: Single Leg Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:34:52.348 [Source: Combination Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:36:28.807 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Starting Futures Order Book Kafka Job...
2025-08-20 16:36:28.809 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:36:29.321 [flink-pekko.actor.default-dispatcher-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:36:29.359 [flink-metrics-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:36:29.375 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:36:29.540 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:36:29.540 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:36:29.600 [flink-pekko.actor.default-dispatcher-6] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:36:29.600 [flink-pekko.actor.default-dispatcher-6] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:36:29.701 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:36:29.701 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:36:29.701 [SourceCoordinator-Source: Trades] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:36:29.847 [Source: Combination Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:36:29.847 [Source: Single Leg Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:36:29.847 [Source: Trades -> Filter (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:40:47.308 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Starting Futures Order Book Kafka Job...
2025-08-20 16:40:47.310 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:40:47.786 [flink-pekko.actor.default-dispatcher-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:40:47.827 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:40:47.847 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:40:48.002 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:40:48.002 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:40:48.059 [flink-pekko.actor.default-dispatcher-5] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:40:48.060 [flink-pekko.actor.default-dispatcher-5] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:40:48.160 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:40:48.160 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:40:48.160 [SourceCoordinator-Source: Trades] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:40:48.312 [Source: Trades -> Filter (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:40:48.312 [Source: Combination Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:40:48.312 [Source: Single Leg Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:44:59.809 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Starting Futures Order Book Kafka Job...
2025-08-20 16:44:59.812 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:45:00.276 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:45:00.316 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:45:00.333 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:45:00.495 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:45:00.495 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:45:00.550 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:45:00.550 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:45:00.660 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:45:00.660 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:45:00.660 [SourceCoordinator-Source: Trades] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:45:00.791 [Source: Trades -> Filter (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:45:00.791 [Source: Combination Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:45:00.791 [Source: Single Leg Orders -> Filter -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
