2025-08-20 16:57:51.768 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:57:51.769 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob -   期货订单簿双层架构系统启动
2025-08-20 16:57:51.769 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - ========================================
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Kafka Bootstrap Servers: localhost:9092
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - Run Mode: kafka
2025-08-20 16:57:51.771 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 
2025-08-20 16:57:51.815 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用Kafka模式，连接到Kafka集群...
2025-08-20 16:57:51.910 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 系统初始化完成，开始处理数据流...
2025-08-20 16:57:51.911 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 等待接收订单数据...

2025-08-20 16:57:52.248 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:57:52.284 [flink-metrics-6] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 16:57:52.299 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:57:52.449 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 16:57:52.450 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 16:57:52.510 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 16:57:52.510 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 16:57:52.606 [SourceCoordinator-Source: Combination Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:57:52.606 [SourceCoordinator-Source: Single Leg Orders] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 16:57:52.730 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - OrderBookProcessFunction initialized for key processing
2025-08-20 16:57:52.749 [Source: Combination Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:57:52.749 [Source: Single Leg Orders -> Filter -> (Map, Sink: Unnamed) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 16:57:57.750 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.function.SimpleOrderBookProcessFunction - Created new DualLayerOrderBookManager
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:57:57.753 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘: 空                               ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:57:57.754 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:02.805 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:02.806 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:02.807 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:07.865 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.866 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:07.867 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╔════════════════════════════════════════╗
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║        订单簿快照 #{:6}             ║
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.912 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 合约代码: {:28} ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 快照时间: {:28} ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 买盘 (前5档):                          ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠────────────────────────────────────────╣
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 卖盘 (前5档):                          ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║   价格: {:8.2f}  数量: {:8.0f}     ║
2025-08-20 16:58:12.913 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╠════════════════════════════════════════╣
2025-08-20 16:58:12.914 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ║ 累计生成快照: {:24} ║
2025-08-20 16:58:12.914 [KeyedProcess -> Sink: Unnamed (1/1)#0] INFO  com.futures.job.FuturesOrderBookKafkaJob$OrderBookSnapshotSink - ╚════════════════════════════════════════╝

