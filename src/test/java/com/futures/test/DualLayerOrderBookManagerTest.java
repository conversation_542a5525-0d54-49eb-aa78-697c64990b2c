package com.futures.test;

import com.futures.pojo.*;
import com.futures.function.DualLayerOrderBookManager;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;

public class DualLayerOrderBookManagerTest {
    
    private DualLayerOrderBookManager manager;
    
    @BeforeEach
    public void setUp() {
        manager = new DualLayerOrderBookManager();
    }
    
    @Test
    public void testSingleLegOrder() {
        // 创建测试订单
        Order order = new Order();
        order.setOrdNbr("TEST001");
        order.setContractCde("IF2504");
        order.setBsTag("B");
        order.setOrdPrc(3800.0);
        order.setOrdVol(10.0);
        order.setRmnVol(10.0);
        order.setOrdSts("3");
        
        // 处理订单
        manager.processSingleLegOrder(order);
        
        // 生成快照
        Map<String, OrderBookSnapshot> snapshots = manager.generateSnapshots();
        
        // 验证
        assertNotNull(snapshots);
        assertTrue(snapshots.containsKey("IF2504"));
        
        OrderBookSnapshot snapshot = snapshots.get("IF2504");
        assertNotNull(snapshot.getBidLevels());
        assertEquals(1, snapshot.getBidLevels().size());
        assertEquals(3800.0, snapshot.getBidLevels().get(0).getPrice());
        assertEquals(10.0, snapshot.getBidLevels().get(0).getVolume());
    }
    
    @Test
    public void testCombinationOrder() {
        // 创建组合委托
        CombinationOrder combOrder = new CombinationOrder();
        combOrder.setOrdNbr("COMB001");
        combOrder.setContractCde("SPD_EB2504_EB2505");
        combOrder.setLeg1ContractCde("EB2504");
        combOrder.setLeg2ContractCde("EB2505");
        combOrder.setBsTag("B");
        combOrder.setOrdPrc(50.0);
        combOrder.setOrdVol(5.0);
        combOrder.setRmnVol(5.0);
        combOrder.setOrdSts("3");
        
        // 处理组合委托
        manager.processCombinationOrder(combOrder);
        
        // 生成快照
        Map<String, OrderBookSnapshot> snapshots = manager.generateSnapshots();
        
        // 验证虚拟订单生成
        assertTrue(snapshots.containsKey("EB2504") || snapshots.containsKey("EB2505"));
        
        System.out.println("Statistics: " + manager.getStatistics());
    }
    
    @Test
    public void testTradeProcessing() {
        // 先添加订单
        Order order = new Order();
        order.setOrdNbr("TEST002");
        order.setContractCde("IF2504");
        order.setBsTag("S");
        order.setOrdPrc(3850.0);
        order.setOrdVol(20.0);
        order.setRmnVol(20.0);
        order.setOrdSts("3");
        manager.processSingleLegOrder(order);
        
        // 创建成交
        Trade trade = new Trade();
        trade.setTrdNbr("TRD001");
        trade.setOrdNbr("TEST002");
        trade.setContractCde("IF2504");
        trade.setTrdPrc(3850.0);
        trade.setTrdVol(5);
        trade.setTrdType("0");

        
        // 生成快照验证
        Map<String, OrderBookSnapshot> snapshots = manager.generateSnapshots();
        OrderBookSnapshot snapshot = snapshots.get("IF2504");
        
        assertNotNull(snapshot);
        assertNotNull(snapshot.getAskLevels());
        
        System.out.println("Order book after trade: " + snapshot);
    }
}