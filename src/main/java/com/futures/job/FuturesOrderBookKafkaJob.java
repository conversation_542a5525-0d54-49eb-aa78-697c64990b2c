package com.futures.job;

import com.futures.function.*;
import com.futures.pojo.*;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.text.SimpleDateFormat;
import java.util.Date;

public class FuturesOrderBookKafkaJob {
    private static final Logger LOG = LoggerFactory.getLogger(FuturesOrderBookKafkaJob.class);
    
    // Kafka配置
    private static final String KAFKA_BOOTSTRAP_SERVERS = System.getProperty("kafka.bootstrap.servers", "localhost:9092");
    private static final String SINGLE_LEG_TOPIC = "singleleg_order_data_event";
    private static final String COMBINATION_TOPIC = "cmb_order_data_event";
    
    // 运行模式
    private static final String RUN_MODE = System.getProperty("run.mode", "kafka"); // kafka or local
    
    public static void main(String[] args) throws Exception {
        LOG.info("========================================");
        LOG.info("  期货订单簿双层架构系统启动");
        LOG.info("========================================");
        LOG.info("Kafka Bootstrap Servers: {}", KAFKA_BOOTSTRAP_SERVERS);
        LOG.info("Run Mode: {}", RUN_MODE);
        LOG.info("");
        
        // 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1); // 单并行度保证顺序
        
        // 配置Checkpoint
        env.enableCheckpointing(10000); // 10秒checkpoint
        
        DataStream<Order> singleLegStream;
        DataStream<CombinationOrder> combinationStream;
        
        if ("local".equals(RUN_MODE)) {
            // 本地测试模式 - 从文件读取
            LOG.info("使用本地测试模式，从JSON文件读取数据...");
            
            singleLegStream = env.fromElements(new Order()) // 占位，实际可从文件读取
                .filter(order -> false); // 暂时不产生数据
            
            combinationStream = env.fromElements(new CombinationOrder())
                .filter(order -> false);
                
        } else {
            // Kafka模式
            LOG.info("使用Kafka模式，连接到Kafka集群...");
            
            try {
                // 创建单腿委托数据源
                KafkaSource<Order> singleLegSource = KafkaSource.<Order>builder()
                    .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                    .setTopics(SINGLE_LEG_TOPIC)
                    .setGroupId("futures-orderbook-single")
                    .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                    .setValueOnlyDeserializer(new OrderDeserializationSchema())
                    .setProperty("max.poll.records", "500")
                    .setProperty("fetch.min.bytes", "1")
                    .setProperty("fetch.max.wait.ms", "100")
                    .setProperty("request.timeout.ms", "5000")
                    .setProperty("session.timeout.ms", "10000")
                    .build();
                
                // 创建组合委托数据源
                KafkaSource<CombinationOrder> combinationSource = KafkaSource.<CombinationOrder>builder()
                    .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                    .setTopics(COMBINATION_TOPIC)
                    .setGroupId("futures-orderbook-combination")
                    .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                    .setValueOnlyDeserializer(new CombinationOrderDeserializationSchema())
                    .setProperty("max.poll.records", "500")
                    .setProperty("fetch.min.bytes", "1")
                    .setProperty("fetch.max.wait.ms", "100")
                    .setProperty("request.timeout.ms", "5000")
                    .setProperty("session.timeout.ms", "10000")
                    .build();
                
                // 创建数据流
                singleLegStream = env.fromSource(
                    singleLegSource, 
                    WatermarkStrategy.forBoundedOutOfOrderness(Duration.ofSeconds(1)),
                    "Single Leg Orders"
                ).filter(order -> order != null);
                
                combinationStream = env.fromSource(
                    combinationSource,
                    WatermarkStrategy.forBoundedOutOfOrderness(Duration.ofSeconds(1)),
                    "Combination Orders"
                ).filter(order -> order != null);
                
            } catch (Exception e) {
                LOG.error("创建Kafka数据源失败: ", e);
                LOG.warn("Kafka连接失败，系统将继续运行但无法接收数据");
                LOG.warn("请确保Kafka服务已启动，或使用 --run.mode=local 参数运行本地模式");
                
                // 创建空数据流
                singleLegStream = env.fromElements(new Order()).filter(order -> false);
                combinationStream = env.fromElements(new CombinationOrder()).filter(order -> false);
            }
        }
        
        // 合并订单流（单腿和组合）
        DataStream<Object> orderStream = singleLegStream.map((MapFunction<Order, Object>) order -> order)
            .union(combinationStream.map((MapFunction<CombinationOrder, Object>) order -> order));
        
        // 创建键控流（按交易所分组，如果没有交易所则使用DEFAULT）
        KeyedStream<Object, String> keyedOrderStream = orderStream
            .keyBy(obj -> {
                if (obj instanceof Order) {
                    String exchCde = ((Order) obj).getExchCde();
                    return (exchCde != null && !exchCde.isEmpty()) ? exchCde : "DEFAULT";
                } else if (obj instanceof CombinationOrder) {
                    String exchCde = ((CombinationOrder) obj).getExchCde();
                    return (exchCde != null && !exchCde.isEmpty()) ? exchCde : "DEFAULT";
                }
                return "DEFAULT";
            });
        
        // 处理订单，生成订单簿快照
        SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream = keyedOrderStream
            .process(new SimpleOrderBookProcessFunction());
        
        // 添加快照输出
        snapshotStream.addSink(new OrderBookSnapshotSink());
        
        // 添加数据统计
        singleLegStream.addSink(new SinkFunction<Order>() {
            private long count = 0;
            @Override
            public void invoke(Order value, Context context) {
                count++;
                if (count % 100 == 0) {
                    LOG.info("已处理单腿委托订单: {} 条", count);
                }
            }
        });
        
        combinationStream.addSink(new SinkFunction<CombinationOrder>() {
            private long count = 0;
            @Override
            public void invoke(CombinationOrder value, Context context) {
                count++;
                if (count % 50 == 0) {
                    LOG.info("已处理组合委托订单: {} 条", count);
                }
            }
        });
        
        // 执行任务
        LOG.info("系统初始化完成，开始处理数据流...");
        LOG.info("等待接收订单数据...\n");
        
        env.execute("Futures Order Book Dual-Layer System");
    }
    
    /**
     * 订单簿快照输出
     */
    private static class OrderBookSnapshotSink implements SinkFunction<OrderBookSnapshot> {
        private static final Logger LOG = LoggerFactory.getLogger(OrderBookSnapshotSink.class);
        private static final SimpleDateFormat SDF = new SimpleDateFormat("HH:mm:ss.SSS");
        private long snapshotCount = 0;
        private long lastDetailLogTime = 0;
        
        @Override
        public void invoke(OrderBookSnapshot snapshot, Context context) throws Exception {
            if (snapshot == null) {
                return;
            }
            
            snapshotCount++;
            long currentTime = System.currentTimeMillis();
            
            // 每5秒输出一次详细信息
            boolean showDetail = (currentTime - lastDetailLogTime >= 5000);
            
            if (showDetail) {
                LOG.info("╔════════════════════════════════════════╗");
                LOG.info("║        订单簿快照 #{:6}             ║", snapshotCount);
                LOG.info("╠════════════════════════════════════════╣");
                LOG.info("║ 合约代码: {:28} ║", snapshot.getContractCode());
                LOG.info("║ 快照时间: {:28} ║", SDF.format(new Date(snapshot.getTimestamp())));
                LOG.info("╠════════════════════════════════════════╣");
                
                // 输出买盘
                if (snapshot.getBidLevels() != null && !snapshot.getBidLevels().isEmpty()) {
                    LOG.info("║ 买盘 (前5档):                          ║");
                    snapshot.getBidLevels().stream()
                        .limit(5)
                        .forEach(level -> 
                            LOG.info("║   价格: {:8.2f}  数量: {:8.0f}     ║", 
                                level.getPrice(), level.getVolume()));
                } else {
                    LOG.info("║ 买盘: 空                               ║");
                }
                
                LOG.info("╠────────────────────────────────────────╣");
                
                // 输出卖盘
                if (snapshot.getAskLevels() != null && !snapshot.getAskLevels().isEmpty()) {
                    LOG.info("║ 卖盘 (前5档):                          ║");
                    snapshot.getAskLevels().stream()
                        .limit(5)
                        .forEach(level -> 
                            LOG.info("║   价格: {:8.2f}  数量: {:8.0f}     ║", 
                                level.getPrice(), level.getVolume()));
                } else {
                    LOG.info("║ 卖盘: 空                               ║");
                }
                
                LOG.info("╠════════════════════════════════════════╣");
                LOG.info("║ 累计生成快照: {:24} ║", snapshotCount);
                LOG.info("╚════════════════════════════════════════╝\n");
                
                lastDetailLogTime = currentTime;
            } else {
                // 简单日志
                LOG.debug("快照 #{} - 合约: {}, 买档: {}, 卖档: {}", 
                    snapshotCount,
                    snapshot.getContractCode(),
                    snapshot.getBidLevels() != null ? snapshot.getBidLevels().size() : 0,
                    snapshot.getAskLevels() != null ? snapshot.getAskLevels().size() : 0);
            }
            
            // 这里可以将快照数据写入数据库、文件或其他存储系统
            // 例如：写入Redis、Elasticsearch、HBase等
        }
    }
}