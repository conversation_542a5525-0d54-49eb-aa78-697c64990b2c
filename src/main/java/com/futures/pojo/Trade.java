package com.futures.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

public class Trade implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("_seq")
    private Long seq;
    
    @JsonProperty("_eno")
    private Long eno;
    
    @JsonProperty("trd_dt")
    private String trdDt;
    
    @JsonProperty("trd_nbr")
    private String trdNbr;
    
    @JsonProperty("b_s_tag")
    private String bsTag;
    
    @JsonProperty("ord_nbr")
    private String ordNbr;
    
    @JsonProperty("memb_cde")
    private String membCde;
    
    @JsonProperty("trd_cde")
    private String trdCde;
    
    @JsonProperty("contract_cde")
    private String contractCde;
    
    @JsonProperty("specu_hedg_tag")
    private String specuHedgTag;
    
    @JsonProperty("ocpos_type")
    private String ocposType;
    
    @JsonProperty("trd_prc")
    private Double trdPrc;
    
    @JsonProperty("trd_vol")
    private Integer trdVol;
    
    @JsonProperty("trd_type")
    private String trdType;
    
    @JsonProperty("trd_tm")
    private String trdTm;
    
    @JsonProperty("trd_tm_millisec")
    private Integer trdTmMillisec;
    
    @JsonProperty("exch_cde")
    private String exchCde;
    
    @JsonProperty("comm_cde")
    private String commCde;
    
    @JsonProperty("event_timestamp")
    private Long eventTimestamp;

    public Trade() {}

    public Long getSeq() {
        return seq;
    }

    public void setSeq(Long seq) {
        this.seq = seq;
    }

    public Long getEno() {
        return eno;
    }

    public void setEno(Long eno) {
        this.eno = eno;
    }

    public String getTrdDt() {
        return trdDt;
    }

    public void setTrdDt(String trdDt) {
        this.trdDt = trdDt;
    }

    public String getTrdNbr() {
        return trdNbr;
    }

    public void setTrdNbr(String trdNbr) {
        this.trdNbr = trdNbr;
    }

    public String getBsTag() {
        return bsTag;
    }

    public void setBsTag(String bsTag) {
        this.bsTag = bsTag;
    }

    public String getOrdNbr() {
        return ordNbr;
    }

    public void setOrdNbr(String ordNbr) {
        this.ordNbr = ordNbr;
    }

    public String getMembCde() {
        return membCde;
    }

    public void setMembCde(String membCde) {
        this.membCde = membCde;
    }

    public String getTrdCde() {
        return trdCde;
    }

    public void setTrdCde(String trdCde) {
        this.trdCde = trdCde;
    }

    public String getContractCde() {
        return contractCde;
    }

    public void setContractCde(String contractCde) {
        this.contractCde = contractCde;
    }

    public String getSpecuHedgTag() {
        return specuHedgTag;
    }

    public void setSpecuHedgTag(String specuHedgTag) {
        this.specuHedgTag = specuHedgTag;
    }

    public String getOcposType() {
        return ocposType;
    }

    public void setOcposType(String ocposType) {
        this.ocposType = ocposType;
    }

    public Double getTrdPrc() {
        return trdPrc;
    }

    public void setTrdPrc(Double trdPrc) {
        this.trdPrc = trdPrc;
    }

    public Integer getTrdVol() {
        return trdVol;
    }

    public void setTrdVol(Integer trdVol) {
        this.trdVol = trdVol;
    }

    public String getTrdType() {
        return trdType;
    }

    public void setTrdType(String trdType) {
        this.trdType = trdType;
    }

    public String getTrdTm() {
        return trdTm;
    }

    public void setTrdTm(String trdTm) {
        this.trdTm = trdTm;
    }

    public Integer getTrdTmMillisec() {
        return trdTmMillisec;
    }

    public void setTrdTmMillisec(Integer trdTmMillisec) {
        this.trdTmMillisec = trdTmMillisec;
    }

    public String getExchCde() {
        return exchCde;
    }

    public void setExchCde(String exchCde) {
        this.exchCde = exchCde;
    }

    public String getCommCde() {
        return commCde;
    }

    public void setCommCde(String commCde) {
        this.commCde = commCde;
    }

    public Long getEventTimestamp() {
        return eventTimestamp;
    }

    public void setEventTimestamp(Long eventTimestamp) {
        this.eventTimestamp = eventTimestamp;
    }

    public boolean isCombinationTrade() {
        return "2".equals(trdType);
    }

    @Override
    public String toString() {
        return "Trade{" +
                "trdNbr='" + trdNbr + '\'' +
                ", ordNbr='" + ordNbr + '\'' +
                ", contractCde='" + contractCde + '\'' +
                ", bsTag='" + bsTag + '\'' +
                ", trdPrc=" + trdPrc +
                ", trdVol=" + trdVol +
                ", trdType='" + trdType + '\'' +
                '}';
    }
}