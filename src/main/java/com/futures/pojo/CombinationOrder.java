package com.futures.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

public class CombinationOrder implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @JsonProperty("_seq")
    private Long seq;
    
    @JsonProperty("_eno")
    private Long eno;
    
    @JsonProperty("trd_dt")
    private String trdDt;
    
    @JsonProperty("ord_nbr")
    private String ordNbr;
    
    @JsonProperty("memb_cde")
    private String membCde;
    
    @JsonProperty("trd_cde")
    private String trdCde;
    
    @JsonProperty("seat_nbr")
    private String seatNbr;
    
    @JsonProperty("contract_cde")
    private String contractCde;
    
    @JsonProperty("cmb_contract_cde")
    private String cmbContractCde;
    
    @JsonProperty("leg_1_contract_cde")
    private String leg1ContractCde;
    
    @JsonProperty("leg_2_contract_cde")
    private String leg2ContractCde;
    
    @JsonProperty("leg_1_comm_cde")
    private String leg1CommCde;
    
    @JsonProperty("leg_2_comm_cde")
    private String leg2CommCde;
    
    @JsonProperty("cmbord_type")
    private String cmbordType;
    
    @JsonProperty("ord_prc_cndt")
    private String ordPrcCndt;
    
    @JsonProperty("b_s_tag")
    private String bsTag;
    
    @JsonProperty("ocpos_type")
    private String ocposType;
    
    @JsonProperty("specu_hedg_tag")
    private String specuHedgTag;
    
    @JsonProperty("ord_prc")
    private Double ordPrc;
    
    @JsonProperty("ord_vol")
    private Double ordVol;
    
    @JsonProperty("ord_sts")
    private String ordSts;
    
    @JsonProperty("ord_type")
    private String ordType;
    
    @JsonProperty("trd_vol")
    private Double trdVol;
    
    @JsonProperty("rmn_vol")
    private Double rmnVol;
    
    @JsonProperty("ord_dt")
    private String ordDt;
    
    @JsonProperty("ord_tm")
    private String ordTm;
    
    @JsonProperty("ord_tm_millisec")
    private Integer ordTmMillisec;
    
    @JsonProperty("ord_tm_microsec")
    private Integer ordTmMicrosec;
    
    @JsonProperty("exch_cde")
    private String exchCde;
    
    @JsonProperty("comm_cde")
    private String commCde;
    
    @JsonProperty("rcod_nbr")
    private Long rcodNbr;
    
    @JsonProperty("event_timestamp")
    private Long eventTimestamp;
    
    public CombinationOrder() {}
    
    public boolean isActive() {
        return "1".equals(ordSts) || "3".equals(ordSts);
    }
    
    public Long getSeq() {
        return seq;
    }
    
    public void setSeq(Long seq) {
        this.seq = seq;
    }
    
    public Long getEno() {
        return eno;
    }
    
    public void setEno(Long eno) {
        this.eno = eno;
    }
    
    public String getTrdDt() {
        return trdDt;
    }
    
    public void setTrdDt(String trdDt) {
        this.trdDt = trdDt;
    }
    
    public String getOrdNbr() {
        return ordNbr;
    }
    
    public void setOrdNbr(String ordNbr) {
        this.ordNbr = ordNbr;
    }
    
    public String getMembCde() {
        return membCde;
    }
    
    public void setMembCde(String membCde) {
        this.membCde = membCde;
    }
    
    public String getTrdCde() {
        return trdCde;
    }
    
    public void setTrdCde(String trdCde) {
        this.trdCde = trdCde;
    }
    
    public String getSeatNbr() {
        return seatNbr;
    }
    
    public void setSeatNbr(String seatNbr) {
        this.seatNbr = seatNbr;
    }
    
    public String getContractCde() {
        return contractCde;
    }
    
    public void setContractCde(String contractCde) {
        this.contractCde = contractCde;
    }
    
    public String getCmbContractCde() {
        return cmbContractCde;
    }
    
    public void setCmbContractCde(String cmbContractCde) {
        this.cmbContractCde = cmbContractCde;
    }
    
    public String getLeg1ContractCde() {
        return leg1ContractCde;
    }
    
    public void setLeg1ContractCde(String leg1ContractCde) {
        this.leg1ContractCde = leg1ContractCde;
    }
    
    public String getLeg2ContractCde() {
        return leg2ContractCde;
    }
    
    public void setLeg2ContractCde(String leg2ContractCde) {
        this.leg2ContractCde = leg2ContractCde;
    }
    
    public String getLeg1CommCde() {
        return leg1CommCde;
    }
    
    public void setLeg1CommCde(String leg1CommCde) {
        this.leg1CommCde = leg1CommCde;
    }
    
    public String getLeg2CommCde() {
        return leg2CommCde;
    }
    
    public void setLeg2CommCde(String leg2CommCde) {
        this.leg2CommCde = leg2CommCde;
    }
    
    public String getCmbordType() {
        return cmbordType;
    }
    
    public void setCmbordType(String cmbordType) {
        this.cmbordType = cmbordType;
    }
    
    public String getOrdPrcCndt() {
        return ordPrcCndt;
    }
    
    public void setOrdPrcCndt(String ordPrcCndt) {
        this.ordPrcCndt = ordPrcCndt;
    }
    
    public String getBsTag() {
        return bsTag;
    }
    
    public void setBsTag(String bsTag) {
        this.bsTag = bsTag;
    }
    
    public String getOcposType() {
        return ocposType;
    }
    
    public void setOcposType(String ocposType) {
        this.ocposType = ocposType;
    }
    
    public String getSpecuHedgTag() {
        return specuHedgTag;
    }
    
    public void setSpecuHedgTag(String specuHedgTag) {
        this.specuHedgTag = specuHedgTag;
    }
    
    public Double getOrdPrc() {
        return ordPrc;
    }
    
    public void setOrdPrc(Double ordPrc) {
        this.ordPrc = ordPrc;
    }
    
    public Double getOrdVol() {
        return ordVol;
    }
    
    public void setOrdVol(Double ordVol) {
        this.ordVol = ordVol;
    }
    
    public String getOrdSts() {
        return ordSts;
    }
    
    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }
    
    public String getOrdType() {
        return ordType;
    }
    
    public void setOrdType(String ordType) {
        this.ordType = ordType;
    }
    
    public Double getTrdVol() {
        return trdVol;
    }
    
    public void setTrdVol(Double trdVol) {
        this.trdVol = trdVol;
    }
    
    public Double getRmnVol() {
        return rmnVol;
    }
    
    public void setRmnVol(Double rmnVol) {
        this.rmnVol = rmnVol;
    }
    
    public String getOrdDt() {
        return ordDt;
    }
    
    public void setOrdDt(String ordDt) {
        this.ordDt = ordDt;
    }
    
    public String getOrdTm() {
        return ordTm;
    }
    
    public void setOrdTm(String ordTm) {
        this.ordTm = ordTm;
    }
    
    public Integer getOrdTmMillisec() {
        return ordTmMillisec;
    }
    
    public void setOrdTmMillisec(Integer ordTmMillisec) {
        this.ordTmMillisec = ordTmMillisec;
    }
    
    public Integer getOrdTmMicrosec() {
        return ordTmMicrosec;
    }
    
    public void setOrdTmMicrosec(Integer ordTmMicrosec) {
        this.ordTmMicrosec = ordTmMicrosec;
    }
    
    public String getExchCde() {
        return exchCde;
    }
    
    public void setExchCde(String exchCde) {
        this.exchCde = exchCde;
    }
    
    public String getCommCde() {
        return commCde;
    }
    
    public void setCommCde(String commCde) {
        this.commCde = commCde;
    }
    
    public Long getRcodNbr() {
        return rcodNbr;
    }
    
    public void setRcodNbr(Long rcodNbr) {
        this.rcodNbr = rcodNbr;
    }
    
    public Long getEventTimestamp() {
        return eventTimestamp;
    }
    
    public void setEventTimestamp(Long eventTimestamp) {
        this.eventTimestamp = eventTimestamp;
    }
    
    @Override
    public String toString() {
        return "CombinationOrder{" +
                "ordNbr='" + ordNbr + '\'' +
                ", contractCde='" + contractCde + '\'' +
                ", leg1='" + leg1ContractCde + '\'' +
                ", leg2='" + leg2ContractCde + '\'' +
                ", bsTag='" + bsTag + '\'' +
                ", ordPrc=" + ordPrc +
                ", rmnVol=" + rmnVol +
                ", ordSts='" + ordSts + '\'' +
                '}';
    }
}