package com.futures.pojo;

import java.io.Serializable;
import java.util.List;

public class OrderBook<PERSON>napshot implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String contractCode;
    private Long timestamp;
    private List<PriceLevel> bidLevels;
    private List<PriceLevel> askLevels;
    
    public OrderBookSnapshot() {}
    
    public String getContractCode() {
        return contractCode;
    }
    
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public List<PriceLevel> getBidLevels() {
        return bidLevels;
    }
    
    public void setBidLevels(List<PriceLevel> bidLevels) {
        this.bidLevels = bidLevels;
    }
    
    public List<PriceLevel> getAskLevels() {
        return askLevels;
    }
    
    public void setAskLevels(List<PriceLevel> askLevels) {
        this.askLevels = askLevels;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OrderBookSnapshot{");
        sb.append("contractCode='").append(contractCode).append('\'');
        sb.append(", timestamp=").append(timestamp);
        sb.append(", bidLevels=").append(bidLevels != null ? bidLevels.size() : 0);
        sb.append(", askLevels=").append(askLevels != null ? askLevels.size() : 0);
        sb.append('}');
        return sb.toString();
    }
}