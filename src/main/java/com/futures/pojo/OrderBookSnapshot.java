package com.futures.pojo;

import java.io.Serializable;
import java.util.List;

public class OrderBookSnapshot implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String contractCode;
    private Long timestamp;
    private List<PriceLevel> bidLevels;
    private List<PriceLevel> askLevels;
    private int baseOrderCount;     // 基础层订单数
    private int virtualOrderCount;  // 虚拟层订单数
    private Double baseVolume;      // 基础层总量
    private Double virtualVolume;   // 虚拟层总量
    
    public OrderBookSnapshot() {}
    
    public String getContractCode() {
        return contractCode;
    }
    
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public List<PriceLevel> getBidLevels() {
        return bidLevels;
    }
    
    public void setBidLevels(List<PriceLevel> bidLevels) {
        this.bidLevels = bidLevels;
    }
    
    public List<PriceLevel> getAskLevels() {
        return askLevels;
    }
    
    public void setAskLevels(List<PriceLevel> askLevels) {
        this.askLevels = askLevels;
    }
    
    public int getBaseOrderCount() {
        return baseOrderCount;
    }
    
    public void setBaseOrderCount(int baseOrderCount) {
        this.baseOrderCount = baseOrderCount;
    }
    
    public int getVirtualOrderCount() {
        return virtualOrderCount;
    }
    
    public void setVirtualOrderCount(int virtualOrderCount) {
        this.virtualOrderCount = virtualOrderCount;
    }
    
    public Double getBaseVolume() {
        return baseVolume;
    }
    
    public void setBaseVolume(Double baseVolume) {
        this.baseVolume = baseVolume;
    }
    
    public Double getVirtualVolume() {
        return virtualVolume;
    }
    
    public void setVirtualVolume(Double virtualVolume) {
        this.virtualVolume = virtualVolume;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OrderBookSnapshot{");
        sb.append("contractCode='").append(contractCode).append('\'');
        sb.append(", timestamp=").append(timestamp);
        sb.append(", bidLevels=").append(bidLevels != null ? bidLevels.size() : 0);
        sb.append(", askLevels=").append(askLevels != null ? askLevels.size() : 0);
        sb.append(", baseOrders=").append(baseOrderCount);
        sb.append(", virtualOrders=").append(virtualOrderCount);
        sb.append('}');
        return sb.toString();
    }
}