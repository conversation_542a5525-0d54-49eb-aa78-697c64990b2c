package com.futures.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Order implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("_seq")
    private Long seq;
    
    @JsonProperty("_eno")
    private Long eno;
    
    @JsonProperty("trd_dt")
    private String trdDt;
    
    @JsonProperty("ord_nbr")
    private String ordNbr;
    
    @JsonProperty("memb_cde")
    private String membCde;
    
    @JsonProperty("trd_cde")
    private String trdCde;
    
    @JsonProperty("seat_nbr")
    private String seatNbr;
    
    @JsonProperty("contract_cde")
    private String contractCde;
    
    @JsonProperty("ord_prc_cndt")
    private String ordPrcCndt;
    
    @JsonProperty("b_s_tag")
    private String bsTag;
    
    @JsonProperty("ocpos_type")
    private String ocposType;
    
    @JsonProperty("specu_hedg_tag")
    private String specuHedgTag;
    
    @JsonProperty("ord_prc")
    private Double ordPrc;
    
    @JsonProperty("ord_vol")
    private Double ordVol;
    
    @JsonProperty("ord_sts")
    private String ordSts;
    
    @JsonProperty("ord_type")
    private String ordType;
    
    @JsonProperty("trd_vol")
    private Double trdVol;
    
    @JsonProperty("rmn_vol")
    private Double rmnVol;
    
    @JsonProperty("ord_dt")
    private String ordDt;
    
    @JsonProperty("ord_tm")
    private String ordTm;
    
    @JsonProperty("ord_tm_millisec")
    private Integer ordTmMillisec;
    
    @JsonProperty("ord_tm_microsec")
    private Integer ordTmMicrosec;
    
    @JsonProperty("exch_cde")
    private String exchCde;
    
    @JsonProperty("comm_cde")
    private String commCde;
    
    @JsonProperty("event_timestamp")
    private Long eventTimestamp;
    
    private boolean isVirtual = false;
    
    public Order() {}

    public Long getSeq() {
        return seq;
    }

    public void setSeq(Long seq) {
        this.seq = seq;
    }

    public Long getEno() {
        return eno;
    }

    public void setEno(Long eno) {
        this.eno = eno;
    }

    public String getTrdDt() {
        return trdDt;
    }

    public void setTrdDt(String trdDt) {
        this.trdDt = trdDt;
    }

    public String getOrdNbr() {
        return ordNbr;
    }

    public void setOrdNbr(String ordNbr) {
        this.ordNbr = ordNbr;
    }

    public String getMembCde() {
        return membCde;
    }

    public void setMembCde(String membCde) {
        this.membCde = membCde;
    }

    public String getTrdCde() {
        return trdCde;
    }

    public void setTrdCde(String trdCde) {
        this.trdCde = trdCde;
    }

    public String getSeatNbr() {
        return seatNbr;
    }

    public void setSeatNbr(String seatNbr) {
        this.seatNbr = seatNbr;
    }

    public String getContractCde() {
        return contractCde;
    }

    public void setContractCde(String contractCde) {
        this.contractCde = contractCde;
    }

    public String getOrdPrcCndt() {
        return ordPrcCndt;
    }

    public void setOrdPrcCndt(String ordPrcCndt) {
        this.ordPrcCndt = ordPrcCndt;
    }

    public String getBsTag() {
        return bsTag;
    }

    public void setBsTag(String bsTag) {
        this.bsTag = bsTag;
    }

    public String getOcposType() {
        return ocposType;
    }

    public void setOcposType(String ocposType) {
        this.ocposType = ocposType;
    }

    public String getSpecuHedgTag() {
        return specuHedgTag;
    }

    public void setSpecuHedgTag(String specuHedgTag) {
        this.specuHedgTag = specuHedgTag;
    }

    public Double getOrdPrc() {
        return ordPrc;
    }

    public void setOrdPrc(Double ordPrc) {
        this.ordPrc = ordPrc;
    }

    public Double getOrdVol() {
        return ordVol;
    }

    public void setOrdVol(Double ordVol) {
        this.ordVol = ordVol;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getOrdType() {
        return ordType;
    }

    public void setOrdType(String ordType) {
        this.ordType = ordType;
    }

    public Double getTrdVol() {
        return trdVol;
    }

    public void setTrdVol(Double trdVol) {
        this.trdVol = trdVol;
    }

    public Double getRmnVol() {
        return rmnVol;
    }

    public void setRmnVol(Double rmnVol) {
        this.rmnVol = rmnVol;
    }

    public String getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(String ordDt) {
        this.ordDt = ordDt;
    }

    public String getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(String ordTm) {
        this.ordTm = ordTm;
    }

    public Integer getOrdTmMillisec() {
        return ordTmMillisec;
    }

    public void setOrdTmMillisec(Integer ordTmMillisec) {
        this.ordTmMillisec = ordTmMillisec;
    }

    public Integer getOrdTmMicrosec() {
        return ordTmMicrosec;
    }

    public void setOrdTmMicrosec(Integer ordTmMicrosec) {
        this.ordTmMicrosec = ordTmMicrosec;
    }

    public String getExchCde() {
        return exchCde;
    }

    public void setExchCde(String exchCde) {
        this.exchCde = exchCde;
    }

    public String getCommCde() {
        return commCde;
    }

    public void setCommCde(String commCde) {
        this.commCde = commCde;
    }

    public Long getEventTimestamp() {
        return eventTimestamp;
    }

    public void setEventTimestamp(Long eventTimestamp) {
        this.eventTimestamp = eventTimestamp;
    }

    public boolean isVirtual() {
        return isVirtual;
    }

    public void setVirtual(boolean virtual) {
        isVirtual = virtual;
    }

    public boolean isActive() {
        return "1".equals(ordSts) || "3".equals(ordSts);
    }

    @Override
    public String toString() {
        return "Order{" +
                "ordNbr='" + ordNbr + '\'' +
                ", contractCde='" + contractCde + '\'' +
                ", bsTag='" + bsTag + '\'' +
                ", ordPrc=" + ordPrc +
                ", rmnVol=" + rmnVol +
                ", ordSts='" + ordSts + '\'' +
                ", isVirtual=" + isVirtual +
                '}';
    }
}