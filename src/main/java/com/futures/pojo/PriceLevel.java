package com.futures.pojo;

import java.io.Serializable;

public class PriceLevel implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Double price;
    private Double volume;
    private Integer orderCount;
    
    public PriceLevel() {}
    
    public PriceLevel(Double price, Double volume, Integer orderCount) {
        this.price = price;
        this.volume = volume;
        this.orderCount = orderCount;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public Double getVolume() {
        return volume;
    }
    
    public void setVolume(Double volume) {
        this.volume = volume;
    }
    
    public Integer getOrderCount() {
        return orderCount;
    }
    
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }
    
    @Override
    public String toString() {
        return String.format("%.2f x %.0f (%d)", price, volume, orderCount);
    }
}