package com.futures.pojo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.ArrayList;
import java.util.Comparator;

public class OrderBook implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private final String contractCode;
    private final Map<Double, List<Order>> bidOrders;
    private final Map<Double, List<Order>> askOrders;
    private final Map<String, Order> orderMap;
    private long lastUpdateTime;
    
    public OrderBook(String contractCode) {
        this.contractCode = contractCode;
        this.bidOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
        this.askOrders = new ConcurrentSkipListMap<>();
        this.orderMap = new ConcurrentHashMap<>();
        this.lastUpdateTime = System.currentTimeMillis();
    }
    
    public void addOrder(Order order) {
        if (order == null || !order.isActive()) {
            return;
        }
        
        orderMap.put(order.getOrdNbr(), order);
        
        Map<Double, List<Order>> targetBook = "B".equals(order.getBsTag()) ? bidOrders : askOrders;
        targetBook.computeIfAbsent(order.getOrdPrc(), k -> new ArrayList<>()).add(order);
        
        lastUpdateTime = System.currentTimeMillis();
    }
    
    public void updateOrder(Order order) {
        if (order == null) {
            return;
        }
        
        Order existingOrder = orderMap.get(order.getOrdNbr());
        if (existingOrder != null) {
            removeOrderFromBook(existingOrder);
        }
        
        if (order.isActive()) {
            addOrder(order);
        } else {
            orderMap.remove(order.getOrdNbr());
        }
        
        lastUpdateTime = System.currentTimeMillis();
    }
    
    public void removeOrder(String ordNbr) {
        Order order = orderMap.remove(ordNbr);
        if (order != null) {
            removeOrderFromBook(order);
        }
        lastUpdateTime = System.currentTimeMillis();
    }
    
    private void removeOrderFromBook(Order order) {
        Map<Double, List<Order>> targetBook = "B".equals(order.getBsTag()) ? bidOrders : askOrders;
        List<Order> orders = targetBook.get(order.getOrdPrc());
        if (orders != null) {
            orders.removeIf(o -> o.getOrdNbr().equals(order.getOrdNbr()));
            if (orders.isEmpty()) {
                targetBook.remove(order.getOrdPrc());
            }
        }
    }
    
    public Map<Double, List<Order>> getBidOrders() {
        return bidOrders;
    }
    
    public Map<Double, List<Order>> getAskOrders() {
        return askOrders;
    }
    
    public Order getOrder(String ordNbr) {
        return orderMap.get(ordNbr);
    }
    
    public String getContractCode() {
        return contractCode;
    }
    
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public int getTotalOrderCount() {
        return orderMap.size();
    }
    
    public OrderBookSnapshot createSnapshot() {
        OrderBookSnapshot snapshot = new OrderBookSnapshot();
        snapshot.setContractCode(contractCode);
        snapshot.setTimestamp(System.currentTimeMillis());
        
        List<PriceLevel> bidLevels = new ArrayList<>();
        for (Map.Entry<Double, List<Order>> entry : bidOrders.entrySet()) {
            PriceLevel level = new PriceLevel();
            level.setPrice(entry.getKey());
            level.setVolume(entry.getValue().stream().mapToDouble(Order::getRmnVol).sum());
            level.setOrderCount(entry.getValue().size());
            bidLevels.add(level);
        }
        snapshot.setBidLevels(bidLevels);
        
        List<PriceLevel> askLevels = new ArrayList<>();
        for (Map.Entry<Double, List<Order>> entry : askOrders.entrySet()) {
            PriceLevel level = new PriceLevel();
            level.setPrice(entry.getKey());
            level.setVolume(entry.getValue().stream().mapToDouble(Order::getRmnVol).sum());
            level.setOrderCount(entry.getValue().size());
            askLevels.add(level);
        }
        snapshot.setAskLevels(askLevels);
        
        return snapshot;
    }
    
    public void clear() {
        bidOrders.clear();
        askOrders.clear();
        orderMap.clear();
        lastUpdateTime = System.currentTimeMillis();
    }
}