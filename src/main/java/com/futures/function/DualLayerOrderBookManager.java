package com.futures.function;

import com.futures.pojo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DualLayerOrderBookManager {
    private static final Logger LOG = LoggerFactory.getLogger(DualLayerOrderBookManager.class);
    
    // 基础层订单簿（单腿委托）
    private final Map<String, OrderBook> baseOrderBooks;
    
    // 虚拟层订单簿（组合委托产生的虚拟单腿）
    private final Map<String, OrderBook> virtualOrderBooks;
    
    // 组合委托缓存
    private final Map<String, CombinationOrder> combinationOrders;
    
    // 最近市场价格缓存（用于计算组合委托的虚拟挂单价格）
    private final Map<String, Double> lastMarketPrices;
    
    public DualLayerOrderBookManager() {
        this.baseOrderBooks = new ConcurrentHashMap<>();
        this.virtualOrderBooks = new ConcurrentHashMap<>();
        this.combinationOrders = new ConcurrentHashMap<>();
        this.lastMarketPrices = new ConcurrentHashMap<>();
    }
    
    /**
     * 处理单腿委托订单
     */
    public void processSingleLegOrder(Order order) {
        if (order == null) {
            return;
        }
        
        String contractCode = order.getContractCde();
        if (contractCode == null || contractCode.isEmpty()) {
            LOG.warn("Order with empty contract code: {}", order);
            return;
        }
        
        // 更新市场价格参考
        updateMarketPriceFromOrder(order);
        
        // 获取或创建基础层订单簿
        OrderBook orderBook = baseOrderBooks.computeIfAbsent(contractCode, k -> new OrderBook(k));
        
        // 更新订单
        orderBook.updateOrder(order);
        
        LOG.debug("Updated single leg order: {} in contract: {}", order.getOrdNbr(), contractCode);
    }
    
    /**
     * 处理组合委托订单
     */
    public void processCombinationOrder(CombinationOrder order) {
        if (order == null) {
            return;
        }
        
        String ordNbr = order.getOrdNbr();
        
        // 如果订单已撤销或完成，移除虚拟挂单
        if ("0".equals(order.getOrdSts()) || "5".equals(order.getOrdSts())) {
            removeVirtualOrders(ordNbr);
            combinationOrders.remove(ordNbr);
            return;
        }
        
        // 缓存组合委托
        combinationOrders.put(ordNbr, order);
        
        // 生成虚拟挂单
        generateVirtualOrders(order);
        
        LOG.debug("Processed combination order: {}", ordNbr);
    }
    
    /**
     * 生成组合委托的虚拟挂单
     */
    private void generateVirtualOrders(CombinationOrder combOrder) {
        String leg1Contract = combOrder.getLeg1ContractCde();
        String leg2Contract = combOrder.getLeg2ContractCde();
        
        if (leg1Contract == null || leg2Contract == null) {
            LOG.warn("Combination order missing leg contracts: {}", combOrder);
            return;
        }
        
        // 获取或创建虚拟订单簿
        OrderBook virtualBook1 = virtualOrderBooks.computeIfAbsent(leg1Contract, k -> new OrderBook(k));
        OrderBook virtualBook2 = virtualOrderBooks.computeIfAbsent(leg2Contract, k -> new OrderBook(k));
        
        // 获取价差价格
        Double spreadPrice = combOrder.getOrdPrc();
        if (spreadPrice == null) {
            return;
        }
        
        // 获取两腿的市场价格
        Double leg1MarketPrice = getMarketPrice(leg1Contract);
        Double leg2MarketPrice = getMarketPrice(leg2Contract);
        
        // 如果没有市场价格，使用默认策略
        if (leg1MarketPrice == null) leg1MarketPrice = 1000.0; // 默认基准价格
        if (leg2MarketPrice == null) leg2MarketPrice = 1000.0;
        
        // 创建第一腿虚拟订单
        Order virtualOrder1 = createVirtualOrder(combOrder, leg1Contract, leg1MarketPrice, spreadPrice, true);
        if (virtualOrder1 != null) {
            LOG.debug("Created virtual order for leg1: {} at price {}", leg1Contract, virtualOrder1.getOrdPrc());
            virtualBook1.updateOrder(virtualOrder1);
        }
        
        // 创建第二腿虚拟订单
        Order virtualOrder2 = createVirtualOrder(combOrder, leg2Contract, leg2MarketPrice, spreadPrice, false);
        if (virtualOrder2 != null) {
            LOG.debug("Created virtual order for leg2: {} at price {}", leg2Contract, virtualOrder2.getOrdPrc());
            virtualBook2.updateOrder(virtualOrder2);
        }
    }
    
    /**
     * 创建虚拟订单
     * 
     * 组合委托价差计算逻辑：
     * - 买入价差（B）：买入近月，卖出远月，期望价差缩小
     *   第一腿（近月）：买入，价格 = 市场价 - 价差调整
     *   第二腿（远月）：卖出，价格 = 市场价 + 价差调整
     * - 卖出价差（S）：卖出近月，买入远月，期望价差扩大
     *   第一腿（近月）：卖出，价格 = 市场价 + 价差调整
     *   第二腿（远月）：买入，价格 = 市场价 - 价差调整
     */
    private Order createVirtualOrder(CombinationOrder combOrder, String contractCode, 
                                    Double marketPrice, Double spreadPrice, boolean isFirstLeg) {
        Order virtualOrder = new Order();
        
        // 使用组合订单号加后缀作为虚拟订单号
        virtualOrder.setOrdNbr(combOrder.getOrdNbr() + (isFirstLeg ? "_L1_V" : "_L2_V"));
        virtualOrder.setContractCde(contractCode);
        
        // 确定买卖方向
        String combBsTag = combOrder.getBsTag();
        String bsTag;
        double priceAdjustment = Math.abs(spreadPrice) / 2.0; // 价差调整量
        double virtualPrice;
        
        if (isFirstLeg) {
            // 第一腿：与组合方向相同
            bsTag = combBsTag;
            if ("B".equals(combBsTag)) {
                // 买入价差：第一腿买入，压低价格
                virtualPrice = marketPrice - priceAdjustment;
            } else {
                // 卖出价差：第一腿卖出，抬高价格
                virtualPrice = marketPrice + priceAdjustment;
            }
        } else {
            // 第二腿：与组合方向相反
            bsTag = "B".equals(combBsTag) ? "S" : "B";
            if ("B".equals(combBsTag)) {
                // 买入价差：第二腿卖出，抬高价格
                virtualPrice = marketPrice + priceAdjustment;
            } else {
                // 卖出价差：第二腿买入，压低价格
                virtualPrice = marketPrice - priceAdjustment;
            }
        }
        
        virtualOrder.setBsTag(bsTag);
        virtualOrder.setOrdPrc(Math.max(0.01, virtualPrice)); // 确保价格为正
        
        // 数量与状态
        virtualOrder.setOrdVol(combOrder.getOrdVol());
        virtualOrder.setRmnVol(combOrder.getRmnVol());
        virtualOrder.setOrdSts(combOrder.getOrdSts());
        virtualOrder.setTrdVol(combOrder.getTrdVol());
        
        // 其他字段
        virtualOrder.setMembCde(combOrder.getMembCde());
        virtualOrder.setTrdCde(combOrder.getTrdCde());
        virtualOrder.setSeatNbr(combOrder.getSeatNbr());
        virtualOrder.setOrdType("V"); // 标记为虚拟订单类型
        virtualOrder.setOrdDt(combOrder.getOrdDt());
        virtualOrder.setOrdTm(combOrder.getOrdTm());
        virtualOrder.setEventTimestamp(combOrder.getEventTimestamp());
        virtualOrder.setExchCde(combOrder.getExchCde());
        
        // 标记为虚拟订单
        virtualOrder.setVirtual(true);
        
        return virtualOrder;
    }
    
    /**
     * 移除虚拟订单
     */
    private void removeVirtualOrders(String combOrdNbr) {
        // 移除两腿的虚拟订单（注意新的后缀格式）
        String virtualOrdNbr1 = combOrdNbr + "_L1_V";
        String virtualOrdNbr2 = combOrdNbr + "_L2_V";
        
        for (OrderBook book : virtualOrderBooks.values()) {
            book.removeOrder(virtualOrdNbr1);
            book.removeOrder(virtualOrdNbr2);
        }
        
        LOG.debug("Removed virtual orders for combination order: {}", combOrdNbr);
    }
    
    /**
     * 更新市场价格（从订单簿获取）
     */
    public void updateMarketPriceFromOrder(Order order) {
        if (order != null && order.getContractCde() != null && order.getOrdPrc() != null) {
            // 使用最新订单价格作为参考
            lastMarketPrices.put(order.getContractCde(), order.getOrdPrc());
        }
    }
    
    /**
     * 获取市场价格
     */
    private Double getMarketPrice(String contractCode) {
        // 优先使用最近成交价
        Double lastPrice = lastMarketPrices.get(contractCode);
        if (lastPrice != null) {
            return lastPrice;
        }
        
        // 其次使用基础层订单簿的中间价
        OrderBook baseBook = baseOrderBooks.get(contractCode);
        if (baseBook != null) {
            Map<Double, List<Order>> bids = baseBook.getBidOrders();
            Map<Double, List<Order>> asks = baseBook.getAskOrders();
            
            if (!bids.isEmpty() && !asks.isEmpty()) {
                Double bestBid = bids.keySet().iterator().next();
                Double bestAsk = asks.keySet().iterator().next();
                return (bestBid + bestAsk) / 2;
            }
        }
        
        return null;
    }
    
    /**
     * 生成合并的订单簿快照
     */
    public Map<String, OrderBookSnapshot> generateSnapshots() {
        Map<String, OrderBookSnapshot> snapshots = new HashMap<>();
        
        // 获取所有合约代码
        Set<String> allContracts = new HashSet<>();
        allContracts.addAll(baseOrderBooks.keySet());
        allContracts.addAll(virtualOrderBooks.keySet());
        
        for (String contractCode : allContracts) {
            OrderBookSnapshot snapshot = generateSnapshot(contractCode);
            if (snapshot != null) {
                snapshots.put(contractCode, snapshot);
            }
        }
        
        return snapshots;
    }
    
    /**
     * 生成单个合约的合并订单簿快照
     */
    private OrderBookSnapshot generateSnapshot(String contractCode) {
        OrderBook baseBook = baseOrderBooks.get(contractCode);
        OrderBook virtualBook = virtualOrderBooks.get(contractCode);
        
        if (baseBook == null && virtualBook == null) {
            return null;
        }
        
        // 合并买卖盘
        Map<Double, DepthInfo> mergedBids = new TreeMap<>(Comparator.reverseOrder());
        Map<Double, DepthInfo> mergedAsks = new TreeMap<>();
        
        int baseOrderCount = 0;
        int virtualOrderCount = 0;
        double baseVolume = 0.0;
        double virtualVolume = 0.0;
        
        // 添加基础层订单
        if (baseBook != null) {
            baseOrderCount = baseBook.getTotalOrderCount();
            for (Map.Entry<Double, List<Order>> entry : baseBook.getBidOrders().entrySet()) {
                double vol = entry.getValue().stream().mapToDouble(o -> o.getRmnVol() != null ? o.getRmnVol() : 0.0).sum();
                baseVolume += vol;
                DepthInfo info = mergedBids.computeIfAbsent(entry.getKey(), k -> new DepthInfo());
                info.baseVolume += vol;
                info.baseOrderCount += entry.getValue().size();
            }
            for (Map.Entry<Double, List<Order>> entry : baseBook.getAskOrders().entrySet()) {
                double vol = entry.getValue().stream().mapToDouble(o -> o.getRmnVol() != null ? o.getRmnVol() : 0.0).sum();
                baseVolume += vol;
                DepthInfo info = mergedAsks.computeIfAbsent(entry.getKey(), k -> new DepthInfo());
                info.baseVolume += vol;
                info.baseOrderCount += entry.getValue().size();
            }
        }
        
        // 添加虚拟层订单
        if (virtualBook != null) {
            virtualOrderCount = virtualBook.getTotalOrderCount();
            for (Map.Entry<Double, List<Order>> entry : virtualBook.getBidOrders().entrySet()) {
                double vol = entry.getValue().stream().mapToDouble(o -> o.getRmnVol() != null ? o.getRmnVol() : 0.0).sum();
                virtualVolume += vol;
                DepthInfo info = mergedBids.computeIfAbsent(entry.getKey(), k -> new DepthInfo());
                info.virtualVolume += vol;
                info.virtualOrderCount += entry.getValue().size();
            }
            for (Map.Entry<Double, List<Order>> entry : virtualBook.getAskOrders().entrySet()) {
                double vol = entry.getValue().stream().mapToDouble(o -> o.getRmnVol() != null ? o.getRmnVol() : 0.0).sum();
                virtualVolume += vol;
                DepthInfo info = mergedAsks.computeIfAbsent(entry.getKey(), k -> new DepthInfo());
                info.virtualVolume += vol;
                info.virtualOrderCount += entry.getValue().size();
            }
        }
        
        // 创建快照
        OrderBookSnapshot snapshot = new OrderBookSnapshot();
        snapshot.setContractCode(contractCode);
        snapshot.setTimestamp(System.currentTimeMillis());
        snapshot.setBaseOrderCount(baseOrderCount);
        snapshot.setVirtualOrderCount(virtualOrderCount);
        snapshot.setBaseVolume(baseVolume);
        snapshot.setVirtualVolume(virtualVolume);
        
        // 转换为PriceLevel列表
        List<PriceLevel> bidLevels = new ArrayList<>();
        for (Map.Entry<Double, DepthInfo> entry : mergedBids.entrySet()) {
            DepthInfo info = entry.getValue();
            PriceLevel level = new PriceLevel();
            level.setPrice(entry.getKey());
            level.setVolume(info.baseVolume + info.virtualVolume);
            level.setOrderCount(info.baseOrderCount + info.virtualOrderCount);
            bidLevels.add(level);
        }
        snapshot.setBidLevels(bidLevels);
        
        List<PriceLevel> askLevels = new ArrayList<>();
        for (Map.Entry<Double, DepthInfo> entry : mergedAsks.entrySet()) {
            DepthInfo info = entry.getValue();
            PriceLevel level = new PriceLevel();
            level.setPrice(entry.getKey());
            level.setVolume(info.baseVolume + info.virtualVolume);
            level.setOrderCount(info.baseOrderCount + info.virtualOrderCount);
            askLevels.add(level);
        }
        snapshot.setAskLevels(askLevels);
        
        return snapshot;
    }
    
    /**
     * 深度信息辅助类
     */
    private static class DepthInfo {
        double baseVolume = 0;
        double virtualVolume = 0;
        int baseOrderCount = 0;
        int virtualOrderCount = 0;
    }
    
    /**
     * 获取统计信息
     */
    public String getStatistics() {
        int baseOrderCount = baseOrderBooks.values().stream()
            .mapToInt(OrderBook::getTotalOrderCount)
            .sum();
        int virtualOrderCount = virtualOrderBooks.values().stream()
            .mapToInt(OrderBook::getTotalOrderCount)
            .sum();
        int combOrderCount = combinationOrders.size();
        
        return String.format("Statistics - Base Orders: %d, Virtual Orders: %d, Combination Orders: %d, Contracts: %d",
            baseOrderCount, virtualOrderCount, combOrderCount, 
            baseOrderBooks.size() + virtualOrderBooks.size());
    }
}