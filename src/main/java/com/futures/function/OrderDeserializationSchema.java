package com.futures.function;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.futures.pojo.Order;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class OrderDeserializationSchema implements DeserializationSchema<Order> {
    private static final Logger LOG = LoggerFactory.getLogger(OrderDeserializationSchema.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Order deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            return objectMapper.readValue(message, Order.class);
        } catch (Exception e) {
            LOG.error("Failed to deserialize order message: {}", new String(message), e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(Order nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<Order> getProducedType() {
        return TypeInformation.of(Order.class);
    }
}