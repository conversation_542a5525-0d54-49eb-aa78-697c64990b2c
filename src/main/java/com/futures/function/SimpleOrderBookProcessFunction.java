package com.futures.function;

import com.futures.pojo.*;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SimpleOrderBookProcessFunction extends KeyedProcessFunction<String, Object, OrderBookSnapshot> {
    private static final Logger LOG = LoggerFactory.getLogger(SimpleOrderBookProcessFunction.class);
    
    private transient ValueState<DualLayerOrderBookManager> orderBookManagerState;
    private transient ValueState<Long> lastSnapshotTimeState;
    
    private static final long SNAPSHOT_INTERVAL_MS = 500; // 0.5秒
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        ValueStateDescriptor<DualLayerOrderBookManager> managerDescriptor = 
            new ValueStateDescriptor<>("orderBookManager", DualLayerOrderBookManager.class);
        orderBookManagerState = getRuntimeContext().getState(managerDescriptor);
        
        ValueStateDescriptor<Long> timeDescriptor = 
            new ValueStateDescriptor<>("lastSnapshotTime", Long.class);
        lastSnapshotTimeState = getRuntimeContext().getState(timeDescriptor);
        
        LOG.info("OrderBookProcessFunction initialized for key processing");
    }
    
    @Override
    public void processElement(Object element, Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        DualLayerOrderBookManager manager = getOrCreateManager();
        
        // 根据类型处理订单
        if (element instanceof Order) {
            Order order = (Order) element;
            manager.processSingleLegOrder(order);
            LOG.debug("Processed single leg order: {}", order.getOrdNbr());
        } else if (element instanceof CombinationOrder) {
            CombinationOrder order = (CombinationOrder) element;
            manager.processCombinationOrder(order);
            LOG.debug("Processed combination order: {}", order.getOrdNbr());
        }
        
        // 检查是否需要生成快照
        checkAndEmitSnapshot(ctx, out);
        
        // 注册定时器
        registerSnapshotTimer(ctx);
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        // 定时生成快照
        emitSnapshots(ctx, out);
        
        // 注册下一个定时器
        registerSnapshotTimer(ctx);
    }
    
    private DualLayerOrderBookManager getOrCreateManager() throws Exception {
        DualLayerOrderBookManager manager = orderBookManagerState.value();
        if (manager == null) {
            manager = new DualLayerOrderBookManager();
            orderBookManagerState.update(manager);
            LOG.info("Created new DualLayerOrderBookManager");
        }
        return manager;
    }
    
    private void checkAndEmitSnapshot(Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        Long lastSnapshotTime = lastSnapshotTimeState.value();
        long currentTime = ctx.timerService().currentProcessingTime();
        
        if (lastSnapshotTime == null || currentTime - lastSnapshotTime >= SNAPSHOT_INTERVAL_MS) {
            emitSnapshots(ctx, out);
        }
    }
    
    private void emitSnapshots(Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        DualLayerOrderBookManager manager = getOrCreateManager();
        
        // 生成所有合约的快照
        int snapshotCount = 0;
        for (OrderBookSnapshot snapshot : manager.generateSnapshots().values()) {
            out.collect(snapshot);
            snapshotCount++;
        }
        
        // 更新最后快照时间
        lastSnapshotTimeState.update(ctx.timerService().currentProcessingTime());
        
        // 输出统计信息
        if (snapshotCount > 0) {
            LOG.debug("Generated {} snapshots - {}", snapshotCount, manager.getStatistics());
        }
    }
    
    private void registerSnapshotTimer(Context ctx) throws Exception {
        Long lastSnapshotTime = lastSnapshotTimeState.value();
        long currentTime = ctx.timerService().currentProcessingTime();
        
        // 计算下次快照时间
        long nextSnapshotTime;
        if (lastSnapshotTime == null) {
            nextSnapshotTime = currentTime + SNAPSHOT_INTERVAL_MS;
        } else {
            nextSnapshotTime = lastSnapshotTime + SNAPSHOT_INTERVAL_MS;
            while (nextSnapshotTime <= currentTime) {
                nextSnapshotTime += SNAPSHOT_INTERVAL_MS;
            }
        }
        
        // 注册定时器
        ctx.timerService().registerProcessingTimeTimer(nextSnapshotTime);
    }
}