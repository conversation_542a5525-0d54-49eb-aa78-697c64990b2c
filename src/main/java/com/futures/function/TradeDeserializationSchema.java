package com.futures.function;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.futures.pojo.Trade;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class TradeDeserializationSchema implements DeserializationSchema<Trade> {
    private static final Logger LOG = LoggerFactory.getLogger(TradeDeserializationSchema.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Trade deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            return objectMapper.readValue(message, Trade.class);
        } catch (Exception e) {
            LOG.error("Failed to deserialize trade message: {}", new String(message), e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(Trade nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<Trade> getProducedType() {
        return TypeInformation.of(Trade.class);
    }
}