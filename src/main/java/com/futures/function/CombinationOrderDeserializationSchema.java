package com.futures.function;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.futures.pojo.CombinationOrder;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class CombinationOrderDeserializationSchema implements DeserializationSchema<CombinationOrder> {
    private static final Logger LOG = LoggerFactory.getLogger(CombinationOrderDeserializationSchema.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        // 配置ObjectMapper忽略未知字段
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    @Override
    public CombinationOrder deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            return objectMapper.readValue(message, CombinationOrder.class);
        } catch (Exception e) {
            LOG.error("Failed to deserialize combination order message: {}", new String(message), e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(CombinationOrder nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<CombinationOrder> getProducedType() {
        return TypeInformation.of(CombinationOrder.class);
    }
}