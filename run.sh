#!/bin/bash

# 期货订单簿系统启动脚本

# 设置环境变量
export JAVA_HOME=${JAVA_HOME:-$(/usr/libexec/java_home)}
export PATH=$JAVA_HOME/bin:$PATH

# 项目根目录
PROJECT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$PROJECT_DIR"

# 日志目录
LOG_DIR="$PROJECT_DIR/logs"
mkdir -p "$LOG_DIR"

# Kafka配置
KAFKA_SERVER=${KAFKA_SERVER:-"localhost:9092"}

echo "========================================"
echo "  期货订单簿重建系统"
echo "========================================"
echo "项目目录: $PROJECT_DIR"
echo "Kafka服务器: $KAFKA_SERVER"
echo ""

# 检查参数
case "$1" in
    build)
        echo "构建项目..."
        mvn clean package -DskipTests
        ;;
        
    test)
        echo "运行测试..."
        mvn test
        ;;
        
    run)
        echo "启动订单簿系统..."
        if [ ! -f "target/futures-orderbook-rebuild-1.0.0.jar" ]; then
            echo "JAR文件不存在，先构建项目..."
            mvn clean package -DskipTests
        fi
        
        java -Xmx2g -Xms1g \
            -Dlog4j.configurationFile=src/main/resources/log4j2.properties \
            -Dkafka.bootstrap.servers="$KAFKA_SERVER" \
            -cp target/futures-orderbook-rebuild-1.0.0.jar \
            com.futures.job.FuturesOrderBookKafkaJob
        ;;
        
    producer)
        echo "启动Kafka数据生产者..."
        shift
        python3 kafka_producer.py --kafka-server "$KAFKA_SERVER" "$@"
        ;;
        
    clean)
        echo "清理项目..."
        mvn clean
        rm -rf logs/*
        ;;
        
    *)
        echo "使用方法: $0 {build|test|run|producer|clean}"
        echo ""
        echo "命令说明:"
        echo "  build    - 构建项目"
        echo "  test     - 运行单元测试"
        echo "  run      - 启动订单簿系统"
        echo "  producer - 启动Kafka数据生产者"
        echo "  clean    - 清理项目"
        echo ""
        echo "示例:"
        echo "  $0 build                     # 构建项目"
        echo "  $0 run                       # 启动系统"
        echo "  $0 producer --clear-topics   # 清理并发送测试数据"
        exit 1
        ;;
esac

exit 0